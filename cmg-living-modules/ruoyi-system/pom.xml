<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cmg</groupId>
        <artifactId>cmg-living-modules</artifactId>
        <version>3.6.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
	
    <artifactId>cmg-living-modules-system</artifactId>

    <description>
        cmg-living-modules-system系统模块
    </description>
	
    <dependencies>
    	
    	<!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        
    	<!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        
    	<!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        
        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        
        <!-- cmg-living Common DataSource -->
        <dependency>
            <groupId>com.cmg</groupId>
            <artifactId>cmg-living-common-datasource</artifactId>
        </dependency>
        
        <!-- cmg-living Common DataScope -->
        <dependency>
            <groupId>com.cmg</groupId>
            <artifactId>cmg-living-common-datascope</artifactId>
        </dependency>
        
        <!-- cmg-living Common Log -->
        <dependency>
            <groupId>com.cmg</groupId>
            <artifactId>cmg-living-common-log</artifactId>
        </dependency>
        
        <!-- cmg-living Common Swagger -->
        <dependency>
            <groupId>com.cmg</groupId>
            <artifactId>cmg-living-common-swagger</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
   
</project>