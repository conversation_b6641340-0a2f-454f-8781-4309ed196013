version : '3.8'
services:
  cmg-living-nacos:
    container_name: cmg-living-nacos
    image: nacos/nacos-server
    build:
      context: ./nacos
    environment:
      - MODE=standalone
    volumes:
      - ./nacos/logs/:/home/<USER>/logs
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      - cmg-living-mysql
  cmg-living-mysql:
    container_name: cmg-living-mysql
    image: mysql:5.7
    build:
      context: ./mysql
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/logs
      - ./mysql/data:/var/lib/mysql
    command: [
          'mysqld',
          '--innodb-buffer-pool-size=80M',
          '--character-set-server=utf8mb4',
          '--collation-server=utf8mb4_unicode_ci',
          '--default-time-zone=+8:00',
          '--lower-case-table-names=1'
        ]
    environment:
      MYSQL_DATABASE: 'ry-cloud'
      MYSQL_ROOT_PASSWORD: password
  cmg-living-redis:
    container_name: cmg-living-redis
    image: redis
    build:
      context: ./redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis/conf/redis.conf:/home/<USER>/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /home/<USER>/redis/redis.conf
  cmg-living-nginx:
    container_name: cmg-living-nginx
    image: nginx
    build:
      context: ./nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/html/dist:/home/<USER>/projects/cmg-living-ui
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - cmg-living-gateway
    links:
      - cmg-living-gateway
  cmg-living-gateway:
    container_name: cmg-living-gateway
    build:
      context: ./cmg-living/gateway
      dockerfile: dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - cmg-living-redis
    links:
      - cmg-living-redis
  cmg-living-auth:
    container_name: cmg-living-auth
    build:
      context: ./cmg-living/auth
      dockerfile: dockerfile
    ports:
      - "9200:9200"
    depends_on:
      - cmg-living-redis
    links:
      - cmg-living-redis
  cmg-living-modules-system:
    container_name: cmg-living-modules-system
    build:
      context: ./cmg-living/modules/system
      dockerfile: dockerfile
    ports:
      - "9201:9201"
    depends_on:
      - cmg-living-redis
      - cmg-living-mysql
    links:
      - cmg-living-redis
      - cmg-living-mysql
  cmg-living-modules-gen:
    container_name: cmg-living-modules-gen
    build:
      context: ./cmg-living/modules/gen
      dockerfile: dockerfile
    ports:
      - "9202:9202"
    depends_on:
      - cmg-living-mysql
    links:
      - cmg-living-mysql
  cmg-living-modules-job:
    container_name: cmg-living-modules-job
    build:
      context: ./cmg-living/modules/job
      dockerfile: dockerfile
    ports:
      - "9203:9203"
    depends_on:
      - cmg-living-mysql
    links:
      - cmg-living-mysql
  cmg-living-modules-file:
    container_name: cmg-living-modules-file
    build:
      context: ./cmg-living/modules/file
      dockerfile: dockerfile
    ports:
      - "9300:9300"
    volumes:
    - ./cmg-living/uploadPath:/home/<USER>/uploadPath
  cmg-living-visual-monitor:
    container_name: cmg-living-visual-monitor
    build:
      context: ./cmg-living/visual/monitor
      dockerfile: dockerfile
    ports:
      - "9100:9100"
